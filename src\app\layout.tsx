import React from "react";
import { Poppins } from "next/font/google";
import "./globals.css";
import "@/fonts/line-awesome-1.3.0/css/line-awesome.css";
import "@/styles/index.scss";
import "rc-slider/assets/index.css";
import type { Metadata, Viewport } from "next";
import AppLayout from "@/layout/AppLayout";
import { Toaster } from "@/components/ui/toaster";
import ClientEffects from "@/app/client-effects";
import { GoogleTagManager } from "@next/third-parties/google";
import { NextIntlClientProvider } from "next-intl";
import { getLocale, getMessages } from "next-intl/server";
import MicrosoftClarity from "@/metrics/MicrosoftClarity";
import { cookies } from "next/headers";

const poppins = Poppins({
  subsets: ["latin"],
  display: "swap",
  weight: ["300", "400", "500", "600", "700"],
});
export const viewport: Viewport = {
  themeColor: [{ media: "(prefers-color-scheme: dark)", color: "#fff" }],
  initialScale: 1,
  width: "device-width",
  minimumScale: 1,
  maximumScale: 1,
  viewportFit: "cover",
  userScalable: false,
  interactiveWidget: "overlays-content",
};
export const metadata: Metadata = {
  title: "PawBooking Pet Booking System",
  description: "Booking online",
  keywords: "PawBooking",
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const locale = await getLocale();
  const cookieStore = cookies();
  const mobileParams = cookieStore.get("mobileParams")?.value || undefined;

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <html lang={locale} className={poppins.className}>
      <GoogleTagManager gtmId="GTM-KGG2HFB5" />
      <MicrosoftClarity />
      <body className="bg-white text-base text-neutral-900 dark:bg-neutral-900 dark:text-neutral-200">
        <AppLayout>
          <NextIntlClientProvider messages={messages}>
            {children}
          </NextIntlClientProvider>
        </AppLayout>
        <Toaster />
        <ClientEffects queryParams={mobileParams} />
      </body>
    </html>
  );
}
