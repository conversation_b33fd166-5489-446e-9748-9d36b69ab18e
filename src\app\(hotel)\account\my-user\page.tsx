import React from "react";
import { cookies } from "next/headers";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import AccountInformations from "./(components)/AccountInformations";

const MyUser = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();

  return (
    <div className="min-h-screen flex justify-center container">
      <AccountInformations
        hotelData={hotelData?.data}
        hotelToken={hotelToken}
      />
    </div>
  );
};

export default MyUser;
