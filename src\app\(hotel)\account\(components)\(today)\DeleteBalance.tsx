"use client";
import React, { useState } from "react";
import type { FC } from "react";
import LoadingSpinner from "@/shared/icons/Spinner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import IconDelete from "@/shared/icons/Delete";
import { Button } from "@/components/ui/button";
import { useBalance } from "@/hooks/hotel/useBalance";

interface DeleteBalanceProps {
  balanceId: string;
  transactionId: string;
  hotelToken: string | undefined;
}

const DeleteBalance: FC<DeleteBalanceProps> = ({
  balanceId,
  transactionId,
  hotelToken,
}) => {
  const { removeBalance } = useBalance();
  const [deleteBalanceIsOpen, setDeleteBalanceIsOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  return (
    <>
      <IconDelete
        className="cursor-pointer size-5 hover:text-secondary-6000 duration-200"
        onClick={() => setDeleteBalanceIsOpen(true)}
      />
      <Dialog open={deleteBalanceIsOpen}>
        <DialogContent onInteractOutside={() => setDeleteBalanceIsOpen(false)}>
          <DialogHeader>
            <DialogTitle>Balance Silme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              removeBalance(
                event,
                balanceId,
                transactionId,
                hotelToken,
                setLoading,
                setDisabled
              )
            }
          >
            <p className="text-gray-500 dark:text-neutral-200 mb-4">
              Seçili balance'ı silmek istiyor musunuz?
            </p>
            <div className="flex justify-end gap-5">
              <Button
                variant="outline"
                type="button"
                onClick={() => setDeleteBalanceIsOpen(false)}
              >
                Vazgeç
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="submit"
                disabled={disabled}
              >
                {loading ? <LoadingSpinner /> : "Onayla"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={() => setDeleteBalanceIsOpen(false)}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DeleteBalance;
