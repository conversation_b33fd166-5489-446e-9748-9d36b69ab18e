"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React, { useRef, useEffect, useState, useMemo, FC } from "react";
import IconChevronRight from "@/shared/icons/ChevronRight";
import IconChevronLeft from "@/shared/icons/ChevronLeft";
import { useTranslations } from "next-intl";
import PawPlus from "@/shared/PawPlus";
import PlusFeatureModal from "@/components/PlusFeatureModal";
interface NavItem {
  route: string;
  title: string;
  slug: string;
  role: string;
  plusFeature: boolean;
}

interface NavProps {
  membershipData: any;
}

const Nav: FC<NavProps> = ({ membershipData }) => {
  const translate = useTranslations("Nav");
  const pathname = usePathname();
  const navContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [modalIsVisible, setModalIsVisible] = useState(false);
  const listNav: NavItem[] = useMemo(
    () => [
      {
        route: `/account`,
        title: translate("navToday"),
        slug: "today",
        role: "today",
        plusFeature: false,
      },
      {
        route: `/account/hotel-informations`,
        title: translate("navHotelİnformations"),
        slug: "otel",
        role: "otel",
        plusFeature: false,
      },
      {
        route: `/account/rooms`,
        title: translate("navRooms"),
        slug: "room",
        role: "otel",
        plusFeature: false,
      },
      {
        route: `/account/services`,
        title: translate("navServices"),
        slug: "service",
        role: "otel",
        plusFeature: false,
      },
      {
        route: `/account/calendar`,
        title: translate("navCalendar"),
        slug: "reservation",
        role: "otel",
        plusFeature: false,
      },
      {
        route: `/account/dashboard`,
        title: translate("navDashboard"),
        slug: "dashboard",
        role: "otel",
        plusFeature: false,
      },
      {
        route: `/account/reservations`,
        title: translate("navReservations"),
        slug: "rez",
        role: "otel",
        plusFeature: false,
      },
      {
        route: `/account/hotel-customer`,
        title: "Otel Müşterileri",
        slug: "hotelCustomer",
        role: "otel",
        plusFeature: true,
      },
      {
        route: `/account/subscriptions`,
        title: "Üye Kartları",
        slug: "subscriptions",
        role: "otel",
        plusFeature: true,
      },
      {
        route: `/account/billing`,
        title: translate("navBilling"),
        slug: "billing",
        role: "otel",
        plusFeature: false,
      },
      {
        route: `/account/user`,
        title: translate("navUser"),
        slug: "user",
        role: "otel",
        plusFeature: false,
      },
      // {
      //   route: `/account/hotel-landing`,
      //   title: translate("navHotelLanding"),
      //   slug: "hotel-website",
      //   role: "otel",
      //   plusFeature: true,,
      // },
      {
        route: `/account/policy`,
        title: translate("navPolicy"),
        slug: "reservation",
        role: "otel",
        plusFeature: false,
      },
    ],
    [translate]
  );

  const handlePlusFeatureClick = () => {
    setModalIsVisible(true);
  };

  //Determines the enabled and disabled states of the right and left buttons.
  const updateScrollState = () => {
    if (navContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = navContainerRef.current;

      const tolerance = 2;
      setCanScrollLeft(scrollLeft > tolerance);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - tolerance);
    }
  };

  const scrollLeft = () => {
    if (navContainerRef.current) {
      navContainerRef.current.scrollBy({ left: -200, behavior: "smooth" });
      setTimeout(updateScrollState, 300);
    }
  };

  const scrollRight = () => {
    if (navContainerRef.current) {
      navContainerRef.current.scrollBy({ left: 200, behavior: "smooth" });
      setTimeout(updateScrollState, 300);
    }
  };

  //Determines the scroll position based on the selected nav item when the page first loads.
  useEffect(() => {
    const navContainer = navContainerRef.current;
    const handleScroll = () => updateScrollState();
    if (navContainer) {
      navContainer.addEventListener("scroll", handleScroll);
      updateScrollState(); // Initial check
    }
    return () => {
      if (navContainer) {
        navContainer.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);

  //Determines the scroll position based on the selected nav item when a nav item is clicked.
  useEffect(() => {
    const index = listNav.findIndex((item: any) => item.route === pathname);
    if (index !== -1 && navContainerRef.current) {
      const itemElement = navContainerRef.current.children[
        index
      ] as HTMLElement;
      const containerRect = navContainerRef.current.getBoundingClientRect();
      const itemRect = itemElement.getBoundingClientRect();
      const scrollAmount =
        itemRect.left -
        containerRect.left -
        containerRect.width / 2 +
        itemRect.width / 2;
      navContainerRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  }, [pathname, listNav]);

  return (
    <div className="container">
      <div className="flex items-center justify-center">
        <button
          className="hidden cursor-pointer disabled:cursor-default md:block"
          disabled={!canScrollLeft}
        >
          <IconChevronLeft
            onClick={scrollLeft}
            className={`mr-2 size-5 text-secondary-6000 ${
              canScrollLeft ? "opacity-100" : "opacity-40"
            }`}
          />
        </button>
        <div
          ref={navContainerRef}
          className="hiddenScrollbar flex space-x-8 overflow-x-auto md:space-x-14 items-center"
        >
          {listNav.map((item: any, index: number) => {
            const isActive =
              index === 0
                ? pathname === item.route
                : pathname.includes(item.route);

            if (membershipData.membershipType !== "plus" && item.plusFeature) {
              return (
                <div
                  key={index}
                  className={`flex flex-row shrink-0 border-b-2 py-5 capitalize text-gray-400 md:py-8 cursor-pointer items-center ${
                    isActive
                      ? "border-secondary-6000 font-medium"
                      : "border-transparent"
                  }`}
                  onClick={handlePlusFeatureClick}
                >
                  <div className="p-1 text-gray-600 mr-0.5">
                    <PawPlus width="22" height="22" />
                  </div>
                  <div className="items-center">{item.title}</div>
                </div>
              );
            }
            return (
              <Link
                key={index}
                href={item.route}
                className={`block shrink-0 border-b-2 py-5 md:py-8 font-medium text-neutral-700 dark:text-neutral-200 ${
                  isActive
                    ? "border-secondary-6000 font-medium"
                    : "border-transparent"
                }`}
              >
                {item.title}
              </Link>
            );
          })}
        </div>
        <button
          className="hidden cursor-pointer bg-transparent disabled:cursor-default md:block"
          disabled={!canScrollRight}
        >
          <IconChevronRight
            onClick={scrollRight}
            className={`ml-2 size-5 bg-transparent text-secondary-6000 ${
              canScrollRight ? "opacity-100" : "opacity-40"
            }`}
          />
        </button>
      </div>
      {modalIsVisible && (
        <PlusFeatureModal
          isOpen={modalIsVisible}
          setIsOpen={setModalIsVisible}
          message="Bu özellik sadece Plus üyelikler için geçerlidir."
        />
      )}
    </div>
  );
};

export default Nav;
