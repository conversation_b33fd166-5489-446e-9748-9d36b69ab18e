"use client";
import React, { useState } from "react";
import type { ChangeEvent, FC } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import Textarea from "@/shared/Textarea";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useHotelService } from "@/hooks/hotel/services/useHotelService";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { isServiceValid, timeMinuteData } from "@/utils/services/hotelServices";
import type { groomingServiceTypes } from "@/types/hotel/services/serviceTypes";
import { Switch } from "@/components/ui/switch";

interface GroomingServiceProps {
  hotelToken: string | undefined;
  closeModal: () => void;
}

const GroomingService: FC<GroomingServiceProps> = ({
  hotelToken,
  closeModal,
}) => {
  const { addService } = useHotelService();
  const [groomingService, setGroomingService] = useState<groomingServiceTypes>({
    isActive: true,
    serviceName: "",
    serviceDetails: {
      duration: "",
    },
    total: "",
    description: "",
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;

    if (["duration"].includes(name)) {
      setGroomingService((prev) => ({
        ...prev,
        serviceDetails: {
          ...prev.serviceDetails,
          [name]: value,
        },
      }));
    } else {
      setGroomingService((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const buttonDisabled = isServiceValid(groomingService);

  return (
    <form
      onSubmit={(event) =>
        addService(
          event,
          hotelToken,
          groomingService,
          "groomingServices",
          setLoading,
          closeModal,
          setDisabled
        )
      }
    >
      <h2 className="font-medium text-lg mb-3">Kuaför Hizmeti</h2>
      <div className="space-y-2">
        <FormItem label="Kuaför Hizmet Adı">
          <Input name="serviceName" onChange={handleChange} />
        </FormItem>
        <p className="font-medium text-neutral-700 dark:text-neutral-300 mt-5 mb-2">
          Hizmet Süresi
        </p>
        <Select
          onValueChange={(selected) =>
            setGroomingService({
              ...groomingService,
              serviceDetails: {
                ...groomingService.serviceDetails,
                duration: selected,
              },
            })
          }
        >
          <SelectTrigger className="max-w-[180px]">
            <SelectValue placeholder="Süre Seç" />
          </SelectTrigger>
          <SelectContent>
            {timeMinuteData.map((time, index) => {
              return (
                <SelectItem key={index} value={time.toString()}>
                  {time} dakika
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
        <FormItem label="Fiyat">
          <Input name="total" type="number" onChange={handleChange} />
        </FormItem>
        <FormItem label="Açıklama">
          <Input name="description" onChange={handleChange} />
        </FormItem>
        <FormItem label="Aktiflik Durumu" className="mt-3">
          <div className="flex gap-2">
            <Switch
              name="isActive"
              checked={groomingService.isActive}
              onCheckedChange={(checked) =>
                setGroomingService((prev) => ({ ...prev, isActive: checked }))
              }
              className="data-[state=unchecked]:bg-red-500 data-[state=checked]:bg-green-500"
            />
            <p
              className={
                groomingService.isActive ? "text-green-500" : "text-red-500"
              }
            >
              {groomingService.isActive ? "Aktif" : "Pasif"}
            </p>
          </div>
        </FormItem>
      </div>
      <div className="mt-7 flex justify-end gap-5">
        <Button onClick={closeModal} variant="outline" type="button">
          İptal
        </Button>
        <Button
          disabled={!buttonDisabled || disabled}
          className="bg-secondary-6000 hover:bg-secondary-700 text-white"
          type="submit"
        >
          {loading ? <LoadingSpinner /> : "Kaydet"}
        </Button>
      </div>
    </form>
  );
};

export default GroomingService;
