import type { FC } from "react";
import React from "react";
import HotelHeader from "@/app/(components)/(Header)/HotelHeader";
import Layout from "@/app/(components)/(hotelHomePage)/Layout";
import PawBookingLogo from "@/shared/PawBookingLogo";

export interface HotelHomePageProps {}

const HotelHomePage: FC<HotelHomePageProps> = () => {
  return (
    <>
      <div className="sticky top-0 z-50 bg-transparent md:block hidden">
        <HotelHeader />
      </div>
      <div className="relative flex justify-center pt-5 md:hidden">
        <PawBookingLogo className="size-20 self-center" />
      </div>
      <div className="z-0">
        <Layout />
      </div>
    </>
  );
};

export default HotelHomePage;
