"use client";

import { useEffect, useState } from "react";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { getBalanceByOrder } from "@/actions/(protected)/hotel/getBalanceByOrder";

interface BalanceTableExpandedRowProps {
  row: any;
  colSpan: number;
}

const BalanceTableExpandedRow = ({
  row,
  colSpan,
}: BalanceTableExpandedRowProps) => {
  const [balanceData, setBalanceData] = useState<any>(null);

  useEffect(() => {
    const fetchBalanceData = async () => {
      const orderId = row.original?._id;
      if (!orderId) return;

      try {
        const data = await getBalanceByOrder(orderId);
        setBalanceData(data?.data?.balance);
      } catch (error) {
        console.error("Error fetching balance data:", error);
        setBalanceData(null);
      }
    };

    fetchBalanceData();
  }, [row.original?._id]);

  return (
    <TableRow className="bg-neutral-100 dark:bg-neutral-600">
      <TableCell colSpan={colSpan} className="p-4">
        <div>
          {!balanceData || !balanceData.transactions ? (
            <div className="text-center"></div>
          ) : (
            <Table className="w-full">
              <TableHeader>
                <TableRow>
                  <TableHead className="font-bold text-center">
                    İşlem Türü
                  </TableHead>
                  <TableHead className="font-bold text-center">
                    İşlem Adı
                  </TableHead>
                  <TableHead className="font-bold text-center">Tarih</TableHead>
                  <TableHead className="font-bold text-center">
                    Ödeme Tipi
                  </TableHead>
                  <TableHead className="font-bold text-center">
                    Açıklama
                  </TableHead>
                  <TableHead className="font-bold text-center">
                    İşlem Tutarı
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {balanceData.transactions.map(
                  (transaction: any, index: number) => (
                    <TableRow key={index}>
                      <TableCell className="text-center">
                        {transaction?.transactionType === 0
                          ? "Alacak"
                          : "Verecek"}
                      </TableCell>
                      <TableCell className="text-center">
                        {(() => {
                          const name = transaction?.transaction;
                          switch (name) {
                            case "reservation":
                              return "Rezervasyon";
                            case "service":
                              return "Hizmet";
                            case "subscription":
                              return "Üyelik Kartı";
                            default:
                              return name || "-";
                          }
                        })()}
                      </TableCell>
                      <TableCell className="text-center">
                        {formatDateToDayMonthYear(transaction?.createdAt) ||
                          "-"}
                      </TableCell>
                      <TableCell className="text-center">
                        {transaction?.paymentType || "-"}
                      </TableCell>
                      <TableCell className="text-center">
                        {transaction?.description || "-"}
                      </TableCell>
                      <TableCell
                        className={`text-center ${
                          transaction?.transactionType === 0
                            ? "text-red-500"
                            : "text-green-500"
                        }`}
                      >
                        {transaction?.transactionType === 0 ? "-" : "+"}
                        {new Intl.NumberFormat("tr-TR", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(Number(transaction?.amount) || 0)}
                        {" ₺"}
                      </TableCell>
                    </TableRow>
                  )
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </TableCell>
    </TableRow>
  );
};

export default BalanceTableExpandedRow;
