{"Page": {"description": "<p>We've unfortunately encountered an error.</p><p>You can try to <retry>reload the page</retry> you were visiting.</p>", "title": "Something went wrong!"}, "Layout": {"description": "This is a basic example that demonstrates the usage of <code>next-intl</code> with the Next.js App Router. Try changing the locale in the top right corner and see how the content changes.", "title": "next-intl example"}, "Component": {"title": "next-intl example"}, "LangDropdown": {"language": "Language"}, "TodayContainer": {"reservation": "Reservation", "revenue": "Revenue", "guest": "Guest", "cancellation": "Cancellation", "yesterday": "<PERSON><PERSON><PERSON>"}, "TodayRoomCard": {"closeAll": "Close All", "openAll": "Open All"}, "LoginPage": {"login": "<PERSON><PERSON>", "hotelLogin": "Hotel Login", "loginUsername": "Username / E-Mail", "loginPassword": "Password", "loginForgotPassword": "Forgot your password?", "loginLogin": "<PERSON><PERSON>", "loginNewuser": "New user?", "loginCreateaccount": "Create account", "loginWelcome": "Welcome"}, "CreateUserContainer": {"signup": "Sign up", "login": "<PERSON><PERSON>", "name": "Name", "validName": "Please enter a valid name", "surname": "Surname", "validSurName": "Please enter a valid surname", "email": "E-Mail", "validEmail": "Please enter a valid e-mail", "dateOfBirth": "Date of birth", "username": "Username", "validUsername": "Your username must be at least 4 characters long and contain only lowercase letters (a-z) and numbers (0-9). Special characters or spaces are not allowed.", "password": "Password", "validPassword": "Your password must be at least 8 characters long and include one uppercase letter, one lowercase letter, and one number. Special characters (!@#$%^&*()_+.-) are allowed, but spaces are not.", "passwordConfirm": "Confirm password", "validPasswordConfirm": "The password and confirm password do not match", "phoneNumber": "Phone number", "phoneNumberInfo": "Enter your phone number with a leading 0", "validPhoneNumber": "Your phone number must start with 0 and should not contain spaces, letters, or special characters.", "genderMale": "Male", "genderFemale": "Female", "genderOther": "I do not want to specify", "account": "Do you have an account?"}, "VerifyAccountContainer": {"accountVerification": "Account Verification", "verificationCode": "Please enter the verification code sent to your email!", "confirm": "Confirm"}, "ForgotPasswordContainer": {"forgotPassword": "Forgot Password", "email": "E-Mail", "emailDesc": "Please enter the email address you are registered with", "validEmail": "Please enter a valid email", "send": "Send", "sendMessage": "The password reset link has been sent to your registered email address.<br /> Please check your email.", "returnHomepage": "Return to home page"}, "ResetPasswordContainer": {"changePassword": "Change Password", "show": "Show", "hide": "<PERSON>de", "newPassword": "New password", "newPasswordValid": "Your password must be at least 8 characters long and include one uppercase letter, one lowercase letter, and one number. Special characters (!@#$%^&*()_+.) are allowed, but spaces are not.", "newPasswordConfirm": "Confirm new password", "newValidPasswordConfirm": "The new password and confirm new password do not match", "save": "Save"}, "AuthHeader": {"back": "Home page", "approve": "If you go back, your information will be reset. Do you confirm?"}, "HotelEmailValidation": {"emailVerification": "Email Verification", "emailAdress": "Please enter your email address", "validEmail": "Please enter a valid email", "send": "Send", "emailCode": "Enter the code sent to your email address", "confirm": "Confirm"}, "HotelPhoneValidation": {"phoneVerification": "Phone Number Verification", "phoneNumber": "Please enter your phone number", "phoneNumberInfo": "Enter your phone number without a 0 at the beginning.", "validPhoneNumber": "Your phone number must start with 0 and should not contain spaces, letters, or special characters.", "send": "Send", "phoneCode": "Enter the code sent to your phone", "confirm": "Confirm", "confirmSendCode": "Do you confirm sending the code?", "change": "Change"}, "CreateHotelContainer": {"send": "Send", "next": "Next", "previous": "Previous"}, "HotelFirstStep": {"hotelOwnerInfo": "Business Owner Information", "name": "Name *", "validName": "Please enter a valid name", "surname": "Surname *", "validSurName": "Please enter a valid surname", "dateOfBirth": "Date of Birth *", "username": "Username *", "validUsername": "Your username must be at least 4 characters long and contain only lowercase letters (a-z) and numbers (0-9). Special characters or spaces are not allowed.", "password": "Password *", "validPassword": "Your password must be at least 8 characters long and include one uppercase letter, one lowercase letter, and one number. Special characters (!@#$%^&*()_+.-) are allowed, but spaces are not.", "passwordConfirm": "Confirm password *", "validPasswordConfirm": "The password and confirm password do not match", "gender": "Gender *", "genderMale": "Male", "genderFemale": "Female", "genderOther": "I do not want to specify"}, "HotelSecondStep": {"hotelInfo": "Business Information", "hotelName": "Business name *", "propertyType": "Business type *", "hotelAddress": "Address", "hotelCity": "City *", "hotelDistrict": "District *", "hotelNeighbourhood": "Neighbourhood *", "hotelNeighbourhoodDesc": "The neighborhood where your hotel is located", "hotelStreet": "Street *", "hotelStreetDesc": "The street where your hotel is located", "hotelBuildingName": "Building Name *", "hotelBuildingNameDesc": "The building where your hotel is located", "hotelBuildingNumber": "Building Number *", "hotelBuildingNumberDesc": "The building number where your hotel is located", "hotelPostalCode": "Postal code *", "hotelPostalCodeDesc": "The postal code of your hotel", "hotelCompanyInfo": "Company Information", "hotelCompanyName": "Company Name *", "hotelCompanyNameDesc": "Your company name", "hotelCompanyTaxNumber": "Tax number *", "hotelCompanyTaxNumberDesc": "The tax number of your hotel", "hotelCompanyTaxOffice": "Tax office *", "hotelCompanyTaxOfficeDesc": "Tax office where your hotel is registered", "hotelPhoneNumber": "Hotel Phone number", "hotelPhoneNumberDesc": "Enter the phone number of your hotel", "hotelDescription": "Hotel Story", "hotelRequired": "All required fields must be filled.", "petHotel": "Pet Hotel", "petTaxi": "Pet <PERSON>", "petGroomer": "<PERSON>", "veterinary": "Veterinary", "petFriendlyHotel": "Pet Friendly Hotel", "trainingFarm": "Training Farm"}, "FileSection": {"hotelUploadHotelLicense": "Upload hotel license", "hotelUploadHotelPhoto": "Upload hotel profile photo"}, "HotelThirdStep": {"hotelFeatures": "Hotel Features", "petTaxi": "Pet taxi", "petCoiffeur": "Pet coiffeur", "petNursery": "Nursery", "pool": "Pool", "playground": "Playground", "dogWalking": "Dog walking", "hotelAcceptedPetTypes": "Accepted pet types", "cat": "Cat", "dog": "Dog", "horse": "Horse", "turtle": "Turtle", "rabbit": "Rabbit", "hotelAdditionalServices": "Additional Services", "veterinary": "Veterinary", "specialNutrition": "Special nutrition", "grooming": "Grooming", "camera": "Camera", "photoInfoServices": "Photo information service", "specialCondition": "Animals with special needs"}, "SuccessPage": {"success": "Your Hotel Registration Has Been Successfully Completed", "returnHomepage": "You can log in or return to the home page", "homepage": "Homepage", "hotelLogin": "Hotel Login"}, "Step4ImagesUpload": {"hotelImages": "Hotel images"}, "Nav": {"navToday": "Today", "navHotelİnformations": "Hotel Informations", "navRooms": "Rooms", "navServices": "Services", "navCalendar": "Calendar", "navDashboard": "Dashboard", "navReservations": "Reservations", "navPromoCode": "Promo Code", "navBilling": "Billing Information", "navUser": "User Information", "navHotelLanding": "Hotel Website", "navPolicy": "Policies and Agreements"}, "UnloggedHeader": {"headerPetOwner": "Do you have a pet?", "headerHotelOwner": "Do you own a hotel?", "headerCreateHotel": "Create Hotel", "headerLogin": "<PERSON><PERSON>", "headerCreateUser": "Create User"}, "PetOwnerAvatarDropdown": {"myAccount": "My account", "myFavorites": "My favorites", "myPets": "My Pets", "darkTheme": "Dark theme", "logout": "Log out"}, "HotelAvatarDropdown": {"myAccount": "My account", "darkTheme": "Dark theme", "logout": "Log out"}, "NavMobile": {"myAccount": "My account", "logout": "Log out", "createAccount": "Create Account", "login": "<PERSON><PERSON>"}, "NavMobileHotel": {"myAccount": "My account", "logout": "Log out"}, "HotelInformationsNav": {"hotelInfo": "Business Information", "hotelAddress": "Business Address", "hotelFeatures": "Business Features", "hotelAcceptedPetTypes": "Accepted Pet Types", "hotelAdditionalServices": "Additional Services", "hotelPhotos": "Business Photos", "hotelDocuments": "Business Documents"}, "HotelInformations": {"hotelName": "Business Name", "hotelStory": "Business Story", "hotelCompanyTaxNumber": "Tax Number", "hotelCompanyTaxOffice": "Tax Office", "hotelLogo": "Business Logo", "save": "Save", "hotelUploadPhoto": "Upload Photo", "hotelTypeImage": "PNG, JPG, GIF Type Image"}, "HotelAddress": {"cityName": "City", "region": "District", "district": "Neighbourhood", "streetName": "Street", "buildingName": "Building Name", "buildingNumber": "Building Number", "postalZone": "Postal Code", "save": "Save"}, "HotelFeatures": {"hotelFeatures": "Hotel Features", "petTaxi": "Pet taxi", "petCoiffeur": "Pet coiffeur", "petGrooming": "Pet grooming", "petNursery": "Nursery", "pool": "Pool", "playground": "Playground", "dogWalking": "Dog walking", "save": "Save", "FeaturesCategories": {"healthAndSafety": "Health and Safety", "healthAndSafetyDescription": "Services that prioritize the safety and health of pets. For example, veterinary support, regular health check-ups, fire alarm systems, and 24/7 monitoring.", "comfortAndAccommodation": "Comfort and Accommodation", "comfortAndAccommodationDescription": "Amenities that ensure pets rest comfortably. For example, custom-designed beds, climate control systems, and spacious accommodation areas.", "activitiesAndEntertainment": "Activities and Entertainment", "activitiesAndEntertainmentDescription": "Services that help pets have fun and stay active. For example, play areas, daily walks, and training programs.", "extraServices": "Extra Services", "extraServicesDescription": "Additional services that enhance pets' accommodation experience. For example, grooming services, special diet meals, and birthday parties."}, "FeaturesLabels": {"veterinaryServices": "24/7 Veterinary services", "cameraSurveillance": "Camera surveillance system", "securityCameras": "Security cameras", "emergencyEquipment": "Emergency equipment", "hygienicToiletAreas": "Hygienic toilet areas", "climateControl": "Climate control systems", "individualRooms": "Individual rooms", "comfortableBeds": "Soft and comfortable beds", "indoorShelters": "Indoor shelters", "outdoorShelters": "Outdoor shelters", "playAreas": "Play areas", "indoorPlayAreas": "Indoor play areas", "gardenArea": "Garden area", "trainingField": "Training field", "playPool": "Play pool", "socializationActivities": "Socialization activities", "liveCameraAccess": "Live camera access", "photoUpdates": "Photo updates", "specialDietMeals": "Special diet meals", "petSpa": "Pet spa and grooming services", "pickupDropoff": "Pickup and drop-off service"}}, "HotelPetTypes": {"hotelAcceptedPetTypes": "Accepted pet types", "cat": "Cat", "smallDogBreed": "Small Dog Breed", "mediumDogBreed": "Medium Dog Breed", "largeDogBreed": "Large Dog Breed", "rabbit": "Rabbit", "hamster": "<PERSON><PERSON>", "guineaPig": "Guinea Pig", "ferret": "<PERSON><PERSON><PERSON>", "hedgehog": "Hedgehog", "chinchilla": "<PERSON><PERSON><PERSON>", "turtle": "Turtle", "snake": "Snake", "iguana": "Iguana", "parrot": "<PERSON><PERSON><PERSON>", "canary": "Canary", "budgerigar": "Budgerigar", "cockatiel": "<PERSON><PERSON><PERSON><PERSON>", "fish": "Fish (Aquarium Species)", "finch": "<PERSON>", "dove": "Dove", "lovebird": "<PERSON>", "freshwaterFish": "Freshwater Fish", "saltwaterFish": "Saltwater Fish", "horse": "Horse", "pony": "Pony", "donkey": "<PERSON><PERSON>", "goat": "Goa<PERSON>", "sheep": "Sheep", "alpaca": "Alpaca", "llama": "Llama", "exoticBird": "Exotic Bird", "monkey": "Monkey", "rooster": "Rooster", "other": "Other", "save": "Save"}, "HotelAdditionalServices": {"hotelAdditionalServices": "Additional Services", "veterinary": "Veterinary", "specialNutrition": "Special nutrition", "grooming": "Grooming", "camera": "Camera", "photoInfoServices": "Photo information service", "specialCondition": "Animals with special needs", "save": "Save"}, "HotelPhotosContainer": {"hotelUploadPhoto": "Upload Photo", "save": "Save"}, "HotelPhotoCard": {"hotelRemovePhoto": "Remove Photo", "hotelDeletePhoto": "Do you want to delete the selected photo?", "hotelCancel": "Cancel", "hotelDelete": "Delete"}, "PostingContainer": {"PostingContainer": "There is no previously created room group. Please create a room group."}, "HotelRoomCard": {"hotelRoomCount": "Number of Rooms:", "roomCapacity": "Room Capacity:", "petType": "Pet Type:"}, "UpdateAndCreateNewServiceModal": {"hotelAddNewRoomGroup": "Add New Service", "hotelAddMultipleRooms": "Add multiple rooms", "hotelRoomName": "Room name", "hotelRoomCapacity": "Room capacity", "hotelRoomGroupName": "Room Group Name", "hotelNumberOfRooms": "Number of Rooms", "hotelRoomNameStartingNumber": "Room Name Starting Number", "petType": "Pet type", "roomFeatures": "Room features", "roomFeaturesDesc": "After typing your room features, press enter or the add button.", "add": "Add", "roomInformation": "Room information area", "roomInformationDesc": "Please provide a brief description of the room", "hotelPhotos": "Hotel Photos", "hotelUploadPhoto": "Upload Photo", "save": "Save", "cancel": "Cancel", "roomUpdate": "Room Update", "cat": "Cat", "dog": "Dog", "roomType": "Room type", "standard": "Standard", "private": "Private", "shared": "Shared"}, "UpdateAndCreateNewRoomModal": {"hotelAddNewRoomGroup": "Add New Room Group", "hotelAddMultipleRooms": "Add multiple rooms", "hotelRoomName": "Room name", "hotelRoomCapacity": "Room capacity", "hotelRoomGroupName": "Room Group Name", "hotelNumberOfRooms": "Number of Rooms", "hotelRoomNameStartingNumber": "Room Name Starting Number", "petType": "Pet type", "roomFeatures": "Room features", "roomFeaturesDesc": "After typing your room features, press enter or the add button.", "add": "Add", "roomInformation": "Room information area", "roomInformationDesc": "Please provide a brief description of the room", "roomPhotos": "Room Photos", "hotelUploadPhoto": "Upload Photo", "save": "Save", "cancel": "Cancel", "roomUpdate": "Room Update", "cat": "Cat", "dog": "Dog", "roomType": "Room type", "standard": "Standard", "private": "Private", "shared": "Shared"}, "DeleteRoomModal": {"RemoveRoom": "Remove Room", "RemoveRoomConfirm": "do you want to remove the room?", "cancel": "Cancel", "confirm": "Confirm"}, "HotelCalendarContainer": {"roomGroups": "Room Groups", "roomGroupName": "Group Name", "NumberOfRooms": "Number of Rooms", "select": "Select"}, "HotelCalendarRange": {"selectRoom": "Select Room", "activeDays": "Active days", "reservedDays": "Reserved days", "passiveDays": "Passive days", "noPriceDays": "Days with no price entered", "locale": "en"}, "HotelCalendarSideBarRange": {"roomCapacity": "Daily Room Capacity", "petType": "Pet Type", "roomType": "Room Type", "clearSelection": "Clear selection", "closetoReservations": "Close to Reservations", "opentoReservations": "Open to Reservations", "locale": "en-EN", "changePrice": "Click to change price", "totalPrice": "Total amount the guest will pay", "clickForDetails": "Click for details"}, "PriceSummary": {"priceBreakdown": "Price Breakdown", "night": "night", "nightlyRate": "Your average nightly rate", "serviceFee": "Service Fee", "totalPrice": "Total amount the guest will pay", "yourEarnings": "Your earnings"}, "UpdateRoomPrice": {"priceUpdate": "Price update", "newRoomRate": "Enter the new room rate", "price": "Price:", "serviceFee": "Service Fee:", "totalPrice": "Total amount the guest will pay:", "yourEarnings": "Your earnings:", "cancel": "Cancel", "save": "Save"}, "HotelCalendarDrawerRangeMobile": {"dateUpdate": "Date Update", "closetoReservations": "Close to Reservations", "opentoReservations": "Open to Reservations", "locale": "en-EN", "changePrice": "Click to change price", "totalPrice": "Total amount the guest will pay", "clickForDetails": "Click for details", "close": "Close", "back": "Back"}, "ReservationList": {"roomName": "Room Name", "petOwner": "Pet Owner", "petName": "Pet Name", "startDate": "Check-in Date", "endDate": "Check-out Date", "channel": "Channel", "date": "Date", "total": "Total", "status": "Status", "action": "Action", "confirmed": "Confirmed", "booked": "Booked", "checkedIn": "Checked In", "declined": "Declined", "waitingForCheckIn": "<PERSON><PERSON><PERSON>", "waitingForCheckOut": "Çıkış Bekleniyor", "waitingForApproval": "<PERSON><PERSON>", "waitingForBook": "In the Cart", "confirm": "Confirm", "reject": "Reject", "reservationNotFound": "Reservation not found.", "showHideColumns": "Show/Hide Columns"}, "BillingContainer": {"companyType": "Company Type *", "soleProprietorship": "Sole proprietorship", "stockCompany": "Joint-stock company", "fullName": "Name Surname *", "alias": "Company Title *", "identityNumber": "Identity Number", "taxNumber": "Tax Number", "birthDate": "Date of Birth", "gsmNumber": "Phone Number *", "address": "Address *", "city": "City *", "district": "District *", "selectCity": "Select City", "selectDistrict": "Select District", "email": "E-Mail *", "authorizedPersonIdentityNumber": "Authorized Person ID Number", "authorizedPersonBirthDate": "Authorized Person Date of Birth", "taxOffice": "Tax Office", "save": "Save", "ibanTitle": "IBAN Title"}, "ReservationActionButtons": {"reservationConfirmation": "Reservation Confirmation", "reservationConfirmationDesc": "Do you want to confirm the selected reservation?", "confirm": "Confirm", "reservationCancellation": "Reservation Cancellation", "reservationCancellationDesc": "Do you want to cancel the selected reservation?", "cancel": "Cancel"}, "ReservationDetail": {"details": "Detail", "reservationDetails": "Reservation Detail", "roomInformation": "Room Information", "roomGroupName": "Room Group Name", "roomName": "Room Name", "roomCapacity": "Room Capacity", "petType": "Pet Type", "accommodationInformation": "Accommodation Information", "checkInDate": "Check-in Date", "checkOutDate": "Check-out Date", "stayDuration": "Stay Duration", "petInformation": "Pet Information", "petName": "Pet Name", "petAge": "Pet Age", "petKind": "Pet Kind", "petBreed": "<PERSON>", "petOwnerInformation": "Pet Owner Information", "petOwnerName": "Pet Owner Name", "phone": "Phone", "email": "E-mail", "night": "night", "vaccinationReport": "Vaccination Report", "showPhoto": "Show Photo"}, "HotelUserTable": {"noUsers": "No users", "firstName": "First Name", "username": "Username", "email": "E-mail", "role": "Role"}, "AddNewAndUpdateHotelUser": {"addUser": "Add User", "addNewUser": "Add New User", "updateUser": "Update User", "firstName": "First Name", "lastName": "Last Name", "gender": "Gender", "male": "Male", "female": "Female", "other": "Other", "username": "Username", "password": "Password", "email": "E-mail", "dateOfBirth": "Date of Birth", "phone": "Mobile Phone Number", "bio": "Bio", "validFirstName": "Please enter a valid first name", "validLastName": "Please enter a valid last name", "validUsername": "Your username must be at least 4 characters long and can only contain lowercase letters (a-z) and numbers (0-9). Special characters or spaces are not allowed.", "validPassword": "Your password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number. Special characters (!@#$%^&*()_+.-) are allowed, but no spaces.", "validEmail": "Please enter a valid email", "validDateOfBirth": "Please enter a valid date of birth", "validPhone": "Please enter a valid phone number", "cancel": "Cancel", "confirm": "Confirm"}, "DeleteHotelUser": {"removeUserTitle": "Remove User", "removeUserConfirmation": "Are you sure you want to remove this user?", "cancel": "Cancel", "confirm": "Confirm"}, "Policy": {"cancellationPolicy": "Cancellation Policy", "checkInStartTime": "Check-in Start Time", "checkInEndTime": "Check-in End Time", "checkOutTime": "Check-out Time", "description": "Description", "cancellationOption": "Cancellation Option", "rules": "Rules", "untilDateRange": "Until a Specific Date", "noCancellation": "No Cancellation", "last24Hours": "Last 24 Hours", "last48Hours": "Last 48 Hours", "lastWeek": "Last Week", "last10Days": "Last 10 Days", "lastMonth": "Last Month", "newRulesAdd": "Add New Rule", "save": "Save"}, "UpdatePolicy": {"updatePolicy": "Update Policy", "updatePolicyTitle": "Policy Update", "description": "Description", "rules": "Rules", "addNewRule": "Add New Rule", "cancel": "Cancel", "save": "Save", "close": "Close"}, "CheckInOutTimePicker": {"checkInStartTimeLabel": "Check-in Start Time", "checkInEndTimeLabel": "Check-in End Time", "checkOutLabel": "Check-out Time", "checkInError": "Check-in end time cannot be earlier than the check-in start time!", "checkOutError": "Check-in time cannot be earlier than check-out time!"}, "AddNewRule": {"newRuleLabel": "New Rule", "ruleTitlePlaceholder": "Rule Title", "ruleDescriptionPlaceholder": "Rule Description", "updateButton": "Update", "addButton": "Add", "cancelButton": "Cancel"}, "CancellationOption": {"cancellationOption": "Cancellation Option", "select": "Select", "noCancellation": "No Cancellation", "cancelUntilDateRange": "Until a Specific Date", "other1": "Convert to Open", "other2": "Insured Booking", "other3": "Cancel Anytime", "other4": "Partial Refund", "dateRange": "Date Range", "last24Hours": "Last 24 Hours", "last48Hours": "Last 48 Hours", "lastWeek": "Last Week", "last10Days": "Last 10 Days", "lastMonth": "Last Month"}, "UserActionButtons": {"editUser": "Edit User", "deleteUser": "Delete User"}, "AcceptDeclineModal": {"cancel": "Cancel"}, "PaginationDropdown": {"pageSelect": "Select Page"}, "ReservationPagination": {"previous": "Previous", "next": "Next"}, "DeleteFileModal": {"title": "Remove File", "confirmMessage": "Do you want to remove the selected file?", "confirmButton": "Confirm", "cancelButton": "Cancel"}, "HotelFiles": {"uploadButtonText": "Upload", "saveButtonText": "Save"}, "FileStep": {"hotelFiles": "Hotel Files", "license": "License", "veterinaryContract": "Veterinary Contract", "taxCertificate": "Tax Documents", "identificationDocumentFront": "ID front", "identificationDocumentBack": "ID back"}, "HotelAccountInformation": {"accountEditInfo": "Edit account information", "userInfo": "User Information", "username": "Username", "email": "Email", "phoneNumber": "Phone Number", "fullName": "Full Name", "role": "Role", "hotelInfo": "Hotel Information", "hotelName": "Hotel Name", "hotelPhoneNumber": "Hotel Phone Number", "taxOffice": "Tax Office", "taxNumber": "Tax Number", "currency": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "accountDeleteButton": "Delete Account and Hotel", "deleteTitle": "Delete Hotel and Account", "deleteDescription": "This action cannot be undone. This will permanently delete your account and remove your data from our servers.", "deleteLabel": "Enter your username to delete your hotel.", "deleteCancel": "Cancel", "deleteButton": "Delete Hotel and Account"}, "LandingPricing": {"subscription": "Pricing", "subscriptionSubtitle": "Choose the plan that suits you best", "free": "Free", "monthly": "Monthly", "yearly": "Yearly", "pawbookingOnly": "Pawbooking Only Reservation", "additionalServices": "Sell Additional Services", "additionalServicesSubtitle": "You can sell additional services such as grooming, veterinary services, and special nutrition to your guests.", "commission": "Pawbooking Reservation Commission", "customerSupport": "PawBooking Customer Support", "customerSupportDescription": "Provided by the PawBooking support team", "advancedContractManagement": "Advanced Contract Management", "customizablePolicies": "Customizable Cancellation and Refund Policies", "startNow": "Start Now", "plus": "Plus", "multiChannelBookings": "Bookings from PawBooking and Other Channels", "multiChannelBookingsDescription": "Phone, Website, Referrals, and Other Channels", "sellAdditionalServices": "Sell Additional Services", "additionalServicesExamples": "Pet Transfer, Training, Pet Daycare, Pet Grooming, Pet Spa", "commissionAdvantage": "Reservation Commission Advantage", "commissionAdvantageDescription": "12% for PawBooking customers, no commission for your own customers", "staffAccounts": "Create Staff Accounts", "staffAccountsDescription": "Accounts for Veterinarians, Trainers, Technicians, and Field Staff", "discountCoupons": "Discount Coupons and Loyalty Programs", "customWebPages": "Custom Web Pages and Content Pages", "multiChannelSupport": "Multi-Channel Customer Support", "privateCloudStorage": "Dedicated Cloud Storage", "privateCloudStorageDescription": "Secure storage area exclusive to hotel customers", "dedicatedSupport": "Active Dedicated Customer Support", "dedicatedSupportDescription": "A customer support team assigned specifically to your business", "features": "Features", "standard": "Standard"}, "LandingGrid": {"reservationManagementName": "Reservation Management", "reservationManagementDescription": "Track and manage your reservations.", "createListingsName": "Create Listings", "createListingsDescription": "List your rooms with rich content.", "guestRelationsName": "Guest Relations", "guestRelationsDescription": "Build connections with your guests.", "calendarName": "Calendar", "calendarDescription": "Use the calendar to filter your files by date."}, "HotelMap": {}, "ServicesSoldList": {"petOwner": "Pet Owner", "petName": "Pet Name", "date": "Date", "total": "Total", "status": "Status", "action": "Action", "confirmed": "Confirmed", "declined": "Declined", "waitingForApproval": "<PERSON><PERSON>", "waitingForBook": "In the Cart", "confirm": "Confirm", "reject": "Reject", "reservationNotFound": "Reservation not found.", "showHideColumns": "Show/Hide Columns", "veterinaryServices": "Veterinary Service", "transportationServices": "Transportation Service", "groomingServices": "Grooming Service"}}