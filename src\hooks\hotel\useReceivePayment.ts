import { HOTEL_API_PATHS } from "@/utils/apiUrls";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";

export const useReceivePayment = () => {
  const { toast } = useToast();
  const router = useRouter();

  const receivePayment = async (
    hotelToken: string | undefined,
    cardBody: any,
    subscriptionType: string | undefined,
    amount: number,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.receivePayment, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            cardBody: cardBody,
            subscriptionType: subscriptionType,
            amount: amount,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata olu<PERSON>.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          throw new Error("Network response was not ok");
        }
        router.push("/checkout/subscription-success");
        setLoading(false);
        return data;
      } catch (error) {
        console.log(error);
      }
    }
  };

  const cardValidationHandler = async (
    hotelToken: string | undefined,
    binNumber: string,
    setValidationResult: any
  ) => {
    if (hotelToken) {
      try {
        const response = await fetch(HOTEL_API_PATHS.checkCardValidation, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ binNumber: binNumber }),
        });
        const data = await response.json();
        setValidationResult(data.data.data);

        if (!response.ok || !data.success) {
          throw new Error("Network response was not ok");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  return { receivePayment, cardValidationHandler };
};
