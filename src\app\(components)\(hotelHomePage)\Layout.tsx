import React from "react";
import Hero from "./Hero";
import { MarqueePartnerHotel } from "@/app/(components)/(hotelHomePage)/Partner-hotel";
import { Grid } from "./Grid";
import { HeroScroll } from "./Container-scroll";
import { AnimatedTestimonialsHotel } from "./Comments";
import FaqSection from "./Faq";
import { PricingSections } from "./pricing";

const HomePageLayout = () => {
  return (
    <div>
      <div className="absolute inset-0 -z-10">
        <div className="h-full w-full bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:35px_35px] opacity-30 [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_110%)]" />
      </div>
      <Hero />
      {/* <MarqueePartnerHotel /> */}

      <Grid />
      <HeroScroll />
      {/* <PricingSections /> */}
      {/* <AnimatedTestimonialsHotel /> */}
      <FaqSection />
    </div>
  );
};

export default HomePageLayout;
