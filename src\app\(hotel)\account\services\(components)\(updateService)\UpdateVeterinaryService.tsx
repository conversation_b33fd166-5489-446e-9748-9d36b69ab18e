"use client";
import React, { useState } from "react";
import type { ChangeEvent, FC } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import IconEdit from "@/shared/icons/Edit";
import type {
  veterinaryServiceTypes,
  veterinaryServiceApiTypes,
} from "@/types/hotel/services/serviceTypes";
import { isServiceValid, timeData } from "@/utils/services/hotelServices";
import { useHotelService } from "@/hooks/hotel/services/useHotelService";
import { Switch } from "@/components/ui/switch";

interface UpdateVeterinaryServiceProps {
  service: veterinaryServiceApiTypes;
  hotelToken: string | undefined;
}

const UpdateVeterinaryService: FC<UpdateVeterinaryServiceProps> = ({
  service,
  hotelToken,
}) => {
  const { updateService } = useHotelService();
  const initialData = {
    isActive: service.serviceData.isActive,
    serviceName: service.serviceData.serviceName,
    total: service.serviceData.total,
    description: service.serviceData.description,
    serviceDetails: {
      veterinarianName: service.serviceData.serviceDetails.veterinarianName,
      requiredDocuments: service.serviceData.serviceDetails.requiredDocuments,
      availabilityStart: service.serviceData.serviceDetails.availabilityStart,
      availabilityEnd: service.serviceData.serviceDetails.availabilityEnd,
    },
  };
  const [veterinaryService, setVeterinaryService] =
    useState<veterinaryServiceTypes>(initialData);
  const [editServiceIsOpen, setEditServiceIsOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;

    if (
      [
        "veterinarianName",
        "requiredDocuments",
        "availabilityStart",
        "availabilityEnd",
      ].includes(name)
    ) {
      setVeterinaryService((prev) => ({
        ...prev,
        serviceDetails: {
          ...prev.serviceDetails,
          [name]: value,
        },
      }));
    } else {
      setVeterinaryService((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const buttonDisabled = isServiceValid(veterinaryService);

  const resetInputs = () => {
    setVeterinaryService(initialData);
    setEditServiceIsOpen(false);
  };

  const closeModal = () => {
    setLoading(false);
    setEditServiceIsOpen(false);
  };

  return (
    <div>
      <IconEdit
        onClick={() => setEditServiceIsOpen(true)}
        className="size-5 cursor-pointer duration-200 hover:text-secondary-6000 text-neutral-500 dark:text-neutral-400"
      />
      <Dialog open={editServiceIsOpen}>
        <DialogContent
          className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-2xl"
          onInteractOutside={resetInputs}
        >
          <DialogHeader>
            <DialogTitle>Servis Güncelleme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              updateService(
                event,
                hotelToken,
                veterinaryService,
                service._id,
                setLoading,
                closeModal,
                setDisabled
              )
            }
          >
            <h2 className="font-medium text-lg mb-3">Veteriner Hizmeti</h2>
            <div className="space-y-2">
              <FormItem label="Veteriner Hizmet Adı">
                <Input
                  name="serviceName"
                  onChange={handleChange}
                  value={veterinaryService.serviceName || ""}
                />
              </FormItem>
              <FormItem label="Veteriner Adı">
                <Input
                  name="veterinarianName"
                  onChange={handleChange}
                  value={
                    veterinaryService.serviceDetails.veterinarianName || ""
                  }
                />
              </FormItem>
              <FormItem label="Gerekli Dosyalar">
                <Input
                  name="requiredDocuments"
                  onChange={handleChange}
                  value={
                    veterinaryService.serviceDetails.requiredDocuments || ""
                  }
                />
              </FormItem>
              <FormItem label="Fiyat">
                <Input
                  name="total"
                  type="number"
                  onChange={handleChange}
                  value={veterinaryService.total || ""}
                />
              </FormItem>
            </div>
            <p className="font-medium text-neutral-700 dark:text-neutral-300 mt-5 mb-2">
              Saat Aralığı
            </p>
            <div className="mb-5">
              <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                Başlangıç
              </p>
              <Select
                onValueChange={(selected) =>
                  setVeterinaryService({
                    ...veterinaryService,
                    serviceDetails: {
                      ...veterinaryService.serviceDetails,
                      availabilityStart: selected,
                    },
                  })
                }
                value={veterinaryService.serviceDetails.availabilityStart || ""}
              >
                <SelectTrigger className="max-w-[180px]">
                  <SelectValue placeholder="Saat Seç" />
                </SelectTrigger>
                <SelectContent>
                  {timeData.map((time, index) => {
                    return (
                      <SelectItem key={index} value={time}>
                        {time}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            <div>
              <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                Bitiş
              </p>
              <Select
                onValueChange={(selected) =>
                  setVeterinaryService({
                    ...veterinaryService,
                    serviceDetails: {
                      ...veterinaryService.serviceDetails,
                      availabilityEnd: selected,
                    },
                  })
                }
                value={veterinaryService.serviceDetails.availabilityEnd || ""}
              >
                <SelectTrigger className="max-w-[180px]">
                  <SelectValue placeholder="Saat Seç" />
                </SelectTrigger>
                <SelectContent>
                  {timeData.map((time, index) => {
                    return (
                      <SelectItem key={index} value={time}>
                        {time}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            <FormItem label="Açıklama" className="mt-3">
              <Input
                name="description"
                onChange={handleChange}
                value={veterinaryService.description || ""}
              />
            </FormItem>
            <FormItem label="Aktiflik Durumu" className="mt-3">
              <div className="flex gap-2">
                <Switch
                  name="isActive"
                  checked={!!veterinaryService.isActive}
                  onCheckedChange={(checked) =>
                    setVeterinaryService((prev) => ({
                      ...prev,
                      isActive: checked,
                    }))
                  }
                  className="data-[state=unchecked]:bg-red-500 data-[state=checked]:bg-green-500"
                />
                <p
                  className={
                    veterinaryService.isActive
                      ? "text-green-500"
                      : "text-red-500"
                  }
                >
                  {veterinaryService.isActive ? "Aktif" : "Pasif"}
                </p>
              </div>
            </FormItem>
            {!buttonDisabled && (
              <span className="text-red-500 text-sm text-right block mt-2">
                Lütfen tüm alanları doldurunuz!
              </span>
            )}
            <div className="mt-7 flex justify-end gap-5">
              <Button onClick={resetInputs} variant="outline" type="button">
                İptal
              </Button>
              <Button
                disabled={
                  JSON.stringify(veterinaryService) ===
                    JSON.stringify(initialData) ||
                  !buttonDisabled ||
                  disabled
                }
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="submit"
              >
                {loading ? <LoadingSpinner /> : "Kaydet"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={resetInputs}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UpdateVeterinaryService;
