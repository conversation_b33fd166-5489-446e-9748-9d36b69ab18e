"use client";
import React from "react";
import type { FC } from "react";
import GallerySlider from "@/components/GallerySlider";
import defaultHotelImage from "@/images/default-hotel-photo.jpg";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useUpdateReservationStatus } from "@/hooks/hotel/useUpdateReservationStatus";
import { PET_TYPES } from "@/app/(enums)/enums";

interface RoomInformationProps {
  selectedRoom: any;
  hotelToken: string | undefined;
  setIsOpen: any;
}

const RoomInformation: FC<RoomInformationProps> = ({
  selectedRoom,
  hotelToken,
  setIsOpen,
}) => {
  const { updateReservationStatusHandler } = useUpdateReservationStatus();

  const handleCheckIn = () => {
    if (selectedRoom?.reservation?._id) {
      updateReservationStatusHandler(
        hotelToken,
        "checkedIn",
        selectedRoom?.reservation?._id
      );
      setIsOpen(false);
      window.location.reload();
    }
  };

  const handleCheckOut = () => {
    if (selectedRoom?.reservation?._id) {
      updateReservationStatusHandler(
        hotelToken,
        "checkedOut",
        selectedRoom?.reservation?._id
      );
      setIsOpen(false);
      window.location.reload();
    }
  };

  const petTypeNames =
    "string" !== typeof selectedRoom.petType
      ? selectedRoom?.petType
          ?.map((petType: any) => PET_TYPES[petType as keyof typeof PET_TYPES])
          .filter(Boolean)
      : [selectedRoom.petType]; // eskiden kalan sadece dog tipi undefined dönüyor onu filterlamak için

  return (
    <div className="mb-4 mt-2 flex flex-col gap-2">
      <div className="my-4 flex w-full justify-center">
        <div className="w-44">
          <GallerySlider
            uniqueID={`StayCard2_${2}`}
            ratioClass="aspect-w-5 aspect-h-2 md:aspect-w-12 md:aspect-h-11"
            galleryImgs={[defaultHotelImage]}
            imageClass="rounded-lg"
          />
        </div>
      </div>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 my-3 md:my-7">
        <div className="flex flex-col items-center rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Oda İsmi</div>
          </div>
          <div>
            <div className="text-sm capitalize">{selectedRoom?.roomName}</div>
          </div>
        </div>
        <div className="flex flex-col items-center rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Oda Kapasitesi</div>
          </div>
          <div>
            <div className="text-sm capitalize">
              {selectedRoom?.roomCapacity}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Oda Pet Tipi</div>
          </div>
          <div>
            <div className="capitalize flex flex-wrap text-sm text-center">
              {petTypeNames.map((pet: any, index: number) => {
                return (
                  <p key={index}>
                    {pet}
                    {index < petTypeNames.length - 1 && <span>,&nbsp;</span>}
                  </p>
                );
              })}
            </div>
          </div>
        </div>
        {(selectedRoom?.reservation?.status === "checkedIn" ||
          selectedRoom?.reservation?.status === "confirmed" ||
          selectedRoom?.reservation?.status === "booked") && (
          <>
            <div className="flex flex-col items-center rounded-lg border py-2 sm:px-4">
              <div>
                <div className="text-sm font-semibold">Check-In</div>
              </div>
              <div>
                <div className="text-sm capitalize">
                  {selectedRoom?.reservation?.startDate}
                </div>
              </div>
            </div>
            <div className="flex flex-col items-center rounded-lg border py-2 sm:px-4">
              <div>
                <div className="text-sm font-semibold">Check-Out</div>
              </div>
              <div>
                <div className="text-sm capitalize">
                  {selectedRoom?.reservation?.endDate}
                </div>
              </div>
            </div>
          </>
        )}
      </div>
      {selectedRoom?.reservation?.status === "confirmed" && (
        <div className="mx-auto mt-3 flex max-w-56 items-center justify-center">
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white"
            onClick={handleCheckIn}
          >
            Check-In Yap
          </Button>
        </div>
      )}
      {selectedRoom?.reservation?.status === "checkedIn" && (
        <div className="mx-auto mt-3 flex max-w-56 items-center justify-center">
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white"
            onClick={handleCheckOut}
          >
            Check-Out Yap
          </Button>
        </div>
      )}
      {selectedRoom?.reservation?.status === "booked" && (
        <Link href="/account/reservations">
          <div className="mx-auto mt-3 flex max-w-56 items-center justify-center">
            <Button>Rezervasyonu Onayla</Button>
          </div>
        </Link>
      )}
      <div>
        {selectedRoom?.allocation === null ? (
          <>
            <div className="flex items-center justify-center">
              Bu oda rezervasyona kapalıdır.
            </div>
            <Link href="/account/calendar">
              <div className="mx-auto mt-3 flex max-w-56 items-center justify-center">
                <Button className="bg-secondary-6000 hover:bg-secondary-700 text-white">
                  Rezervasyona Aç
                </Button>
              </div>
            </Link>
          </>
        ) : selectedRoom?.reservation === null ? (
          <>
            <div className="flex items-center justify-center">
              Bu oda rezerve edilebilir.
            </div>
            <Link href="/account/calendar">
              <div className="mx-auto mt-3 flex max-w-56 items-center justify-center">
                <Button className="bg-secondary-6000 hover:bg-secondary-700 text-white">
                  Rezervasyon Oluştur
                </Button>
              </div>
            </Link>
          </>
        ) : null}
      </div>
    </div>
  );
};

export default RoomInformation;
