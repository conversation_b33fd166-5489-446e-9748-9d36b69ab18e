"use client";
import React, { useState } from "react";
import type { ColumnDef, SortingState } from "@tanstack/react-table";
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import AddBalance from "./AddBalance";
import DeleteBalance from "./DeleteBalance";

const BalanceInformation = ({
  selectedRoom,
  hotelToken,
}: {
  selectedRoom: any;
  hotelToken: string | undefined;
}) => {
  const [sorting, setSorting] = useState<SortingState>([]);

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: "description",
      header: "Açıklama",
      cell: ({ row }) => row.getValue("description") || "-",
    },
    {
      accessorKey: "paymentType",
      header: "Ödeme Türü",
      cell: ({ row }) => row.getValue("paymentType") || "-",
    },
    {
      accessorKey: "amount",
      header: "Tutar",
      cell: ({ row }) => {
        const amount = Number(row.getValue("amount"));
        const formattedAmount = new Intl.NumberFormat("tr-TR", {
          style: "currency",
          currency: "TRY",
        }).format(amount);

        return (
          <div
            className={`text-right font-medium ${row.original.transactionType === 1 ? "text-green-600" : "text-red-500"}`}
          >
            {row.original.transactionType === 0
              ? formattedAmount
              : `-${formattedAmount}`}
          </div>
        );
      },
    },
    {
      accessorKey: "delete",
      header: "",
      cell: ({ row }) => {
        return (
          <DeleteBalance
            balanceId={selectedRoom?.reservation?.balance?._id}
            transactionId={row.original._id}
            hotelToken={hotelToken}
          />
        );
      },
    },
  ];

  const table = useReactTable({
    data: selectedRoom?.reservation?.balance?.balanceList || [],
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    state: { sorting },
  });

  if (!selectedRoom?.reservation?.balance)
    return (
      <>
        <p className="text-center text-sm mt-5">Sonuç bulunamadı</p>
        <div className="flex justify-between items-center mt-4 p-4 border-t border-gray-300 fixed bottom-[65px] md:bottom-0 left-0 w-full">
          <div className="font-semibold text-sm md:text-lg basis-1/3">
            Toplam Bakiye :
          </div>
          <AddBalance
            balanceId={selectedRoom?.reservation?.balance?._id}
            hotelToken={hotelToken}
          />
          <div
            className={`md:text-lg font-semibold basis-1/3 text-end ${
              selectedRoom?.reservation?.balance?.balanceAmount >= 0
                ? "text-green-600"
                : "text-red-600"
            }`}
          >
            {new Intl.NumberFormat("tr-TR", {
              style: "currency",
              currency: "TRY",
            }).format(selectedRoom?.reservation?.balance?.balanceAmount || 0)}
          </div>
        </div>
      </>
    );

  return (
    <div className="w-full overflow-auto max-h-96 md:max-h-[550px] pb-20 overscroll-auto">
      <div className="rounded-md border">
        <Table className="overflow-scroll">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Sonuç bulunamadı.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex justify-between items-center mt-4 p-4 border-t border-gray-300 fixed bottom-[65px] md:bottom-0 left-0 w-full bg-white">
        <div className="font-semibold text-sm md:text-lg basis-1/3">
          Toplam Bakiye :
        </div>
        <AddBalance
          balanceId={selectedRoom?.reservation?.balance?._id}
          hotelToken={hotelToken}
        />
        <div
          className={`md:text-lg font-semibold basis-1/3 text-end ${
            selectedRoom?.reservation?.balance?.balanceAmount >= 0
              ? "text-green-600"
              : "text-red-600"
          }`}
        >
          {new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
          }).format(selectedRoom?.reservation?.balance?.balanceAmount || 0)}
        </div>
      </div>
    </div>
  );
};

export default BalanceInformation;
