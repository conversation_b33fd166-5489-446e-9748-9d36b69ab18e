"use client";
import type { FC, ChangeEvent } from "react";
import { useState } from "react";
import React from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import { useHotelCalendarActions } from "@/hooks/hotel/useHotelCalendar";
import { useTranslations } from "next-intl";
import LoadingSpinner from "@/shared/icons/Spinner";
import { Checkbox } from "@/components/ui/checkbox";
interface UpdateRoomPriceMobileProps {
  setNewRoomPrice: React.Dispatch<React.SetStateAction<string | undefined>>;
  newRoomPrice: string | undefined;
  hotelToken: string | undefined;
  startDateToString: string | undefined;
  endDateToString: string | undefined;
  selectedRoomId: string | string[] | undefined;
  setFetchAgain: React.Dispatch<React.SetStateAction<boolean>>;
  setSelectedNumber: React.Dispatch<React.SetStateAction<number>>;
}

const UpdateRoomPriceMobile: FC<UpdateRoomPriceMobileProps> = ({
  setNewRoomPrice,
  newRoomPrice,
  hotelToken,
  startDateToString,
  endDateToString,
  selectedRoomId,
  setFetchAgain,
  setSelectedNumber,
}) => {
  const translate = useTranslations("UpdateRoomPrice");
  const { setRoomPrice } = useHotelCalendarActions();
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [priceValue, setPriceValue] = useState<string | undefined>("");
  const [isChecked, setIsChecked] = useState<boolean>(false);

  const closeModal = () => {
    setNewRoomPrice("");
    setSelectedNumber(0);
    setLoading(false);
    setPriceValue("");
    setIsChecked(false);
  };

  const newRoomPriceHandler = (event: ChangeEvent<HTMLInputElement>) => {
    const price = event.target.value;

    if (+price < 0) {
      return;
    }
    
    setPriceValue(price);
    if (isChecked) {
      const adjustedPrice = +price / (1 + 18 / 100);
      setNewRoomPrice(adjustedPrice.toString());
    } else {
      setNewRoomPrice(price);
    }
  };
  return (
    <>
      {!isChecked ? (
        <FormItem
          className="!text-neutral-700 dark:!text-neutral-400"
          label={translate("newRoomRate")}
        >
          <Input
            onChange={newRoomPriceHandler}
            type="number"
            value={priceValue}
          />
        </FormItem>
      ) : (
        <FormItem
          className="!text-neutral-700 dark:!text-neutral-400"
          label={"Misafirin ödeyeceği fiyatı giriniz"}
        >
          <Input
            onChange={newRoomPriceHandler}
            type="number"
            value={priceValue}
          />
        </FormItem>
      )}
      <div className="flex items-center gap-2 mt-2">
        <Checkbox
          onClick={() => {
            setIsChecked((prev) => !prev);
            setNewRoomPrice("");
            setPriceValue("");
          }}
          checked={isChecked}
        />
        <span className="text-sm text-neutral-700 dark:text-neutral-400">
          veya misafirin ödeyeceği fiyatı gir
        </span>
      </div>
      <div className="space-y-4">
        <div className="mt-2 flex gap-1 text-sm">
          <div className="font-semibold text-neutral-700 dark:text-neutral-400">
            {translate("price")}
          </div>
          <div className="font-medium">
            {newRoomPrice
              ? new Intl.NumberFormat("tr-TR", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }).format(Number(newRoomPrice)) + "₺"
              : "0₺"}
          </div>
        </div>
        <div className="mt-2 flex gap-1 text-sm">
          <div className="font-semibold text-neutral-700 dark:text-neutral-400">
            {translate("serviceFee")}
          </div>
          <div className="font-medium">
            {newRoomPrice
              ? new Intl.NumberFormat("tr-TR", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }).format(Number(newRoomPrice) * 0.18) + "₺"
              : "0₺"}
          </div>
        </div>
        {!isChecked ? (
          <div className="mt-2 flex gap-1 text-sm">
            <div className="font-semibold text-neutral-700 dark:text-neutral-400">
              {translate("totalPrice")}
            </div>
            <div className="font-medium">
              {newRoomPrice
                ? new Intl.NumberFormat("tr-TR", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  }).format(
                    Number(newRoomPrice) + Number(Number(newRoomPrice) * 0.18)
                  ) + "₺"
                : "0₺"}
            </div>
          </div>
        ) : (
          <div className="mt-2 flex gap-1 text-sm">
            <div className="font-semibold text-neutral-700 dark:text-neutral-400">
              Yeni oda fiyatı:
            </div>
            <div className="font-medium">
              {newRoomPrice
                ? new Intl.NumberFormat("tr-TR", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  }).format(Number(priceValue) / 1.18) + "₺"
                : "0₺"}
            </div>
          </div>
        )}
        <div className="mt-2 flex gap-1 text-sm">
          <div className="font-semibold text-neutral-700 dark:text-neutral-400">
            {translate("yourEarnings")}
          </div>
          <div className="font-medium">
            {newRoomPrice
              ? new Intl.NumberFormat("tr-TR", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }).format(Number(newRoomPrice)) + "₺"
              : "0₺"}
          </div>
        </div>
      </div>

      <div className="mt-5 flex justify-end gap-5">
        <Button
          className="w-full"
          variant="outline"
          type="button"
          onClick={closeModal}
        >
          {translate("cancel")}
        </Button>
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
          disabled={disabled || !newRoomPrice}
          onClick={() => {
            setRoomPrice(
              hotelToken,
              startDateToString,
              endDateToString,
              selectedRoomId,
              newRoomPrice,
              setFetchAgain,
              closeModal,
              setLoading,
              setDisabled
            );
          }}
          type="button"
        >
          {loading ? <LoadingSpinner /> : translate("save")}
        </Button>
      </div>
    </>
  );
};

export default UpdateRoomPriceMobile;
