"use client";

import { Document, Page, Text, View, Image } from "@react-pdf/renderer";
import PawBooking<PERSON>ogo from "public/img/pawlogo.png";
import { styles } from "./pdfStyles";
import { formatCellValue } from "./BalanceTableUtils";

interface BalanceTablePDFProps {
  data: any[];
  columns: any[];
}

const BalanceTablePDF = ({ data, columns }: BalanceTablePDFProps) => {
  const visibleColumns = columns.filter(
    (col) => col.getIsVisible() && col.id !== "expander"
  );

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Logo */}
        <View style={styles.logoContainer}>
          <Image src={PawBookingLogo.src} style={styles.logo} />
        </View>

        <View style={styles.table}>
          {/* Table Header */}
          <View style={[styles.tableRow]}>
            {visibleColumns.map((col) => (
              <Text key={col.id} style={[styles.tableCell, styles.headerCell]}>
                {typeof col.columnDef.header === "string"
                  ? col.columnDef.header
                  : col.id}
              </Text>
            ))}
          </View>
          {/* Table Body */}
          {data.map((row, index) => (
            <View key={index} style={styles.tableRow}>
              {visibleColumns.map((col) => {
                // Özel durum: petOwner_fullName için
                if (col.id === "petOwner_fullName") {
                  return (
                    <Text key={col.id} style={styles.tableCell}>
                      {row.petOwner?.fullName || "-"}
                    </Text>
                  );
                }
                return (
                  <Text key={col.id} style={styles.tableCell}>
                    {formatCellValue(row[col.id], col.id)}
                  </Text>
                );
              })}
            </View>
          ))}
        </View>
      </Page>
    </Document>
  );
};

export default BalanceTablePDF;
