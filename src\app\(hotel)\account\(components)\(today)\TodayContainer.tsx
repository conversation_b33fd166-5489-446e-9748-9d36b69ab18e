"use client";
import React from "react";
import TodayRoomCard from "./TodayRoomCard";
import { useTranslations } from "next-intl";
import StatsCard from "@/shared/StatsCard";

const TodayContainer = ({
  roomGroupData,
  hotelToken,
  todayData,
  membershipData,
}: {
  roomGroupData: any;
  todayData: any;
  hotelToken: string | undefined;
  membershipData: any;
}) => {
  const translate = useTranslations("TodayContainer");
  const { todayReservations, todayRevenue, totalGuest, todayCancelled } =
    todayData;

  return (
    <div className="w-full">
      <div className="mx-auto mb-10 grid grid-cols-2 lg:grid-cols-4 gap-4 max-lg:overflow-auto max-lg:py-5">
        <StatsCard
          title={translate("reservation")}
          todayValue={todayReservations?.todayReservations}
          yesterdayValue={todayReservations?.yesterdayReservations}
          percentageChange={todayReservations?.percentageChange}
          icon="IconCalendar"
        />
        <StatsCard
          title={translate("revenue")}
          todayValue={`${parseFloat(todayRevenue?.todayRevenue)}₺`}
          yesterdayValue={`${parseFloat(todayRevenue?.yesterdayRevenue)}₺`}
          percentageChange={`${todayRevenue?.percentageChange}`}
          price={true}
          icon="IconChart"
        />
        <StatsCard
          title={translate("guest")}
          todayValue={totalGuest?.totalGuests}
          totalRoom={totalGuest?.totalRooms}
          icon="IconPawGuest"
        />
        <StatsCard
          title={translate("cancellation")}
          todayValue={todayCancelled?.cancelledCount}
          icon="IconCancel"
        />
      </div>
      {roomGroupData?.data?.length > 0 && (
        <TodayRoomCard
          roomGroupData={roomGroupData.data}
          hotelToken={hotelToken}
          membershipData={membershipData}
        />
      )}
    </div>
  );
};

export default TodayContainer;
