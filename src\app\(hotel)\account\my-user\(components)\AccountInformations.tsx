import React from "react";
import type { FC } from "react";
import { useTranslations } from "next-intl";
import DeleteHotel from "./DeleteHotel";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

interface AccountInformationsProps {
  hotelData: HotelDataApiTypes;
  hotelToken: string | undefined;
}

const AccountInformations: FC<AccountInformationsProps> = ({
  hotelData,
  hotelToken,
}) => {
  const translate = useTranslations("HotelAccountInformation");
  return (
    <div className="w-full max-w-lg bg-white text-gray-800 rounded-lg shadow-md overflow-hidden h-[fit-content]">
      <div className="bg-secondary-6000 p-6 text-center">
        <h1 className="text-2xl font-semibold text-white"></h1>
        <p className="mt-1 text-white font-medium capitalize">
          {translate("accountEditInfo")}
        </p>
      </div>
      <div className="p-6 space-y-4 border-Kullanıcı Profilib">
        <h2 className="text-lg font-semibold text-gray-700">
          {translate("userInfo")}
        </h2>
        <div className="flex max-md:flex-col max-md:items-start items-center justify-between">
          <span className="font-medium text-gray-500">
            {translate("username")}:
          </span>
          <span className="font-medium">{hotelData?.users[0].username}</span>
        </div>
        <div className="flex max-md:flex-col max-md:items-start items-center justify-between">
          <span className="font-medium text-gray-500">
            {translate("email")}:
          </span>
          <span className="font-medium">{hotelData?.users[0].email}</span>
        </div>
        <div className="flex max-md:flex-col max-md:items-start items-center justify-between">
          <span className="font-medium text-gray-500">
            {translate("phoneNumber")}:
          </span>
          <span className="font-medium">{hotelData?.users[0].phone}</span>
        </div>
        <div className="flex max-md:flex-col max-md:items-start items-center justify-between">
          <span className="font-medium text-gray-500">
            {translate("fullName")}:
          </span>
          <span className="font-medium">{hotelData?.users[0].fullName}</span>
        </div>
        <div className="flex max-md:flex-col max-md:items-start items-center justify-between">
          <span className="font-medium text-gray-500">
            {translate("role")}:
          </span>
          <span className="font-medium">{hotelData?.users[0].role}</span>
        </div>
      </div>
      <div className="p-6 space-y-4 border-b">
        <h2 className="text-lg font-semibold text-gray-700">
          {translate("hotelInfo")}
        </h2>
        <div className="flex max-md:flex-col max-md:items-start items-center justify-between">
          <span className="font-medium text-gray-500">
            {translate("hotelName")}:
          </span>
          <span className="font-medium capitalize">{hotelData?.hotelName}</span>
        </div>
        <div className="flex max-md:flex-col max-md:items-start items-center justify-between">
          <span className="font-medium text-gray-500">
            {translate("hotelPhoneNumber")}:
          </span>
          <span className="font-medium">{hotelData?.hotelPhoneNumber}</span>
        </div>
        <div className="flex max-md:flex-col max-md:items-start items-center justify-between">
          <span className="font-medium text-gray-500">
            {translate("taxOffice")}:
          </span>
          <span className="font-medium">{hotelData?.taxOffice}</span>
        </div>
        <div className="flex max-md:flex-col max-md:items-start items-center justify-between">
          <span className="font-medium text-gray-500">
            {translate("taxNumber")}:
          </span>
          <span className="font-medium">{hotelData?.taxNumber}</span>
        </div>
        <div className="flex max-md:flex-col max-md:items-start items-center justify-between">
          <span className="font-medium text-gray-500">
            {translate("currency")}:
          </span>
          <span className="font-medium">{hotelData?.currency}</span>
        </div>
        <div className="flex max-md:flex-col max-md:items-start items-center justify-between">
          <span className="font-medium text-gray-500">
            {translate("status")}:
          </span>
          <span className="font-medium capitalize">{hotelData?.status}</span>
        </div>
      </div>
      <DeleteHotel
        username={hotelData?.users[0]?.username}
        hotelToken={hotelToken}
      />
    </div>
  );
};

export default AccountInformations;
