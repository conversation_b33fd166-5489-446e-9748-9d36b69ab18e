"use client";
import React, { useState } from "react";
import type { ChangeEvent, FC } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import IconEdit from "@/shared/icons/Edit";
import type {
  transportationServiceTypes,
  transportationServiceApiTypes,
} from "@/types/hotel/services/serviceTypes";
import { isServiceValid } from "@/utils/services/hotelServices";
import { useHotelService } from "@/hooks/hotel/services/useHotelService";
import { Switch } from "@/components/ui/switch";

interface UpdateTransportationServiceProps {
  service: transportationServiceApiTypes;
  hotelToken: string | undefined;
}

const UpdateTransportationService: FC<UpdateTransportationServiceProps> = ({
  service,
  hotelToken,
}) => {
  const { updateService } = useHotelService();
  const initialData = {
    isActive: service.serviceData.isActive,
    serviceName: service.serviceData.serviceName,
    description: service.serviceData.description,
    serviceDetails: {
      initialPrice: service.serviceData.serviceDetails.initialPrice,
      maxDistance: service.serviceData.serviceDetails.maxDistance,
      distancePrice: service.serviceData.serviceDetails.distancePrice,
    },
  };
  const [transportationService, setTransportationService] =
    useState<transportationServiceTypes>(initialData);
  const [editServiceIsOpen, setEditServiceIsOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const formFields = [
    { label: "Taşıma Hizmet Adı", name: "serviceName" },
    { label: "Başlangıç Fiyatı", name: "initialPrice", type: "number" },
    { label: "Maksimum Mesafe KM", name: "maxDistance", type: "number" },
    { label: "KM Başı Fiyat", name: "distancePrice", type: "number" },
    { label: "Açıklama", name: "description" },
  ];

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;

    if (["initialPrice", "maxDistance", "distancePrice"].includes(name)) {
      setTransportationService((prev) => ({
        ...prev,
        serviceDetails: {
          ...prev.serviceDetails,
          [name]: value,
        },
      }));
    } else {
      setTransportationService((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const buttonDisabled = isServiceValid(transportationService);

  const closeModal = () => {
    setLoading(false);
    setEditServiceIsOpen(false);
  };

  const resetInputs = () => {
    setTransportationService(initialData);
    setEditServiceIsOpen(false);
  };

  return (
    <div>
      <IconEdit
        onClick={() => setEditServiceIsOpen(true)}
        className="size-5 cursor-pointer duration-200 hover:text-secondary-6000 text-neutral-500 dark:text-neutral-400"
      />
      <Dialog open={editServiceIsOpen}>
        <DialogContent
          className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-2xl"
          onInteractOutside={resetInputs}
        >
          <DialogHeader>
            <DialogTitle>Hizmet Güncelleme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              updateService(
                event,
                hotelToken,
                transportationService,
                service._id,
                setLoading,
                closeModal,
                setDisabled
              )
            }
          >
            <h2 className="font-medium text-lg mb-3">Taşıma Hizmeti</h2>
            <div className="space-y-2">
              {formFields.map(({ label, name, type = "text" }) => (
                <FormItem key={name} label={label}>
                  <Input
                    name={name}
                    type={type}
                    value={
                      ["initialPrice", "maxDistance", "distancePrice"].includes(
                        name
                      )
                        ? String(
                            transportationService.serviceDetails[
                              name as keyof typeof transportationService.serviceDetails
                            ] || ""
                          )
                        : String(
                            transportationService[
                              name as keyof transportationServiceTypes
                            ] || ""
                          )
                    }
                    onChange={handleChange}
                  />
                </FormItem>
              ))}
              <FormItem label="Aktiflik Durumu">
                <div className="flex gap-2">
                  <Switch
                    name="isActive"
                    checked={!!transportationService.isActive}
                    onCheckedChange={(checked) =>
                      setTransportationService((prev) => ({
                        ...prev,
                        isActive: checked,
                      }))
                    }
                    className="data-[state=unchecked]:bg-red-500 data-[state=checked]:bg-green-500"
                  />
                  <p
                    className={
                      transportationService.isActive
                        ? "text-green-500"
                        : "text-red-500"
                    }
                  >
                    {transportationService.isActive ? "Aktif" : "Pasif"}
                  </p>
                </div>
              </FormItem>
            </div>
            {!buttonDisabled && (
              <span className="text-red-500 text-sm text-right block mt-2">
                Lütfen tüm alanları doldurunuz!
              </span>
            )}
            <div className="mt-7 flex justify-end gap-5">
              <Button onClick={resetInputs} variant="outline" type="button">
                İptal
              </Button>
              <Button
                disabled={
                  JSON.stringify(transportationService) ===
                    JSON.stringify(initialData) ||
                  !buttonDisabled ||
                  disabled
                }
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="submit"
              >
                {loading ? <LoadingSpinner /> : "Kaydet"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={resetInputs}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UpdateTransportationService;
