import React from "react";
import type { FC } from "react";
import Image from "next/image";
import { Separator } from "@/components/ui/separator";
import { formatPhoneNumber } from "./TodayRoomCard";
import petOwnerAvatar from "@/images/avatars/Image-1.png";

interface VetInformationProps {
  selectedRoom: any;
}

const VetInformation: FC<VetInformationProps> = ({ selectedRoom }) => {
  return (
    <div className="">
      <Separator className="my-2" />

      <div className="flex max-md:flex-col justify-center items-center gap-2 py-3 mt-4 shadow-sm rounded-md border">
        <div className="flex justify-center">
          <div className="rounded-full border-2">
            <Image
              src={petOwnerAvatar}
              width={100}
              height={100}
              alt="catPhoto"
              className="rounded-full object-cover"
            />
          </div>
        </div>
        <div>
          <div className="mt-2 flex gap-1">
            <div className="flex flex-row items-center">
              <div className="text-sm font-semibold">Veteriner:</div>
            </div>
            <div className="text-sm">
              {`${selectedRoom.reservation?.pet?.vetName || ""} ${selectedRoom.reservation?.pet?.vetLastName || ""}`}
            </div>
          </div>
          <div className="mt-2 flex gap-1">
            <div className="flex flex-row items-center">
              <div className="text-sm font-semibold">E-posta:</div>
            </div>
            <div className="text-sm">
              {selectedRoom.reservation?.pet?.vetEmail}
            </div>
          </div>
          <div className="mt-2 flex gap-1">
            <div className="flex flex-row items-center">
              <div className="text-sm font-semibold">Telefon:</div>
            </div>
            <div className="text-sm">
              {formatPhoneNumber(
                selectedRoom.reservation?.pet?.vetPhoneNumber || ""
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VetInformation;
